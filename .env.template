# JWT Configuration
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
VALID_ISSUER=cattlytics-ai
VALID_AUDIENCE=cattlytics-users

# Swagger Configuration
SWAGGER_USERNAME=
SWAGGER_PASSWORD=

# Backend URL
APP_CORE_BE_URL=http://localhost:8000

# API Configuration
API_V1_STR=/api
PROJECT_NAME=Cattlytics AI
VERSION=1.0.0
DESCRIPTION=Cattlytics AI Knowledge Base System

# OpenAI Configuration
OPENAI_API_KEY=

# Qdrant Configuration
QDRANT_HOST=https://efd8b5e5-78f4-4d71-8510-14d7d593c77f.us-west-1-0.aws.cloud.qdrant.io
QDRANT_PORT=6333
QDRANT_API_KEY=
QDRANT_COLLECTION_NAME=cattlytics_knowledge_base

# Knowledge Base Configuration
EMBEDDING_MODEL=text-embedding-3-small
RESPONSE_MODEL=gpt-4o-mini