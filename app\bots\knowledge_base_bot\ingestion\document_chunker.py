"""
Document Chunker with heading-based chunking for UI documentation.

This module handles intelligent chunking of parsed documents by splitting
on headings to maintain semantic coherence for software documentation
and user interface scenarios.
"""

from typing import List, Dict, Any, Optional, Set, Tuple
import re
import asyncio
from dataclasses import dataclass
from enum import Enum
from core.logger import logger

from ..config import ChunkingConfig
from .document_parser import ParsedDocument


class UIFeatureType(Enum):
    """UI feature categories for filtering chunks."""
    VIEW_MANAGEMENT = "view_management"
    SEARCH_FILTER = "search_filter"
    DATA_ENTRY = "data_entry"
    ANIMAL_DETAILS = "animal_details"
    NAVIGATION = "navigation"
    GENERAL = "general"


@dataclass
class DocumentChunk:
    """
    Container for a document chunk with metadata and UI feature classification.
    """
    content: str
    metadata: Dict[str, Any]
    ui_features: Set[UIFeatureType]
    chunk_index: int
    start_char: int
    end_char: int
    word_count: int
    heading_level: int
    heading_text: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary format."""
        return {
            "content": self.content,
            "metadata": self.metadata,
            "ui_features": [feature.value for feature in self.ui_features],
            "chunk_index": self.chunk_index,
            "start_char": self.start_char,
            "end_char": self.end_char,
            "word_count": self.word_count,
            "heading_level": self.heading_level,
            "heading_text": self.heading_text
        }


class DocumentChunker:
    """
    Document chunker with intelligent UI feature-based filtering.

    Chunks documents into manageable pieces while maintaining semantic coherence
    and classifying chunks by UI features for improved retrieval.
    """
    
    def __init__(self, config: ChunkingConfig):
        """
        Initialize the document chunker.

        Args:
            config: Chunking configuration
        """
        self.config = config
        self._compile_feature_patterns()
        logger.info("Document chunker initialized with heading-based chunking")

    def _compile_feature_patterns(self):
        """Compile regex patterns for UI feature detection."""
        self.feature_patterns = {}

        # UI feature classification patterns
        feature_keywords = {
            UIFeatureType.VIEW_MANAGEMENT: [
                "view", "display", "card", "table", "list", "grid", "layout",
                "switch", "toggle", "change", "show", "hide", "sort", "order"
            ],
            UIFeatureType.SEARCH_FILTER: [
                "search", "find", "filter", "criteria", "query", "eartag",
                "tag", "id", "identifier", "gender", "breed", "age", "group"
            ],
            UIFeatureType.DATA_ENTRY: [
                "add", "create", "new", "upload", "import", "bulk", "form",
                "template", "excel", "file", "data", "fill", "enter", "input"
            ],
            UIFeatureType.ANIMAL_DETAILS: [
                "details", "profile", "records", "information", "health",
                "breeding", "movement", "financial", "history", "tracking"
            ],
            UIFeatureType.NAVIGATION: [
                "navigate", "click", "open", "page", "link", "button",
                "cattle listing", "listing page", "menu", "go to", "access"
            ]
        }

        for feature_type, keywords in feature_keywords.items():
            # Create case-insensitive pattern for each feature
            pattern = r'\b(?:' + '|'.join(re.escape(keyword) for keyword in keywords) + r')\b'
            self.feature_patterns[feature_type] = re.compile(pattern, re.IGNORECASE)
    
    async def chunk(self, parsed_document: ParsedDocument, additional_metadata: Optional[Dict[str, Any]] = None) -> List[DocumentChunk]:
        """
        Chunk a parsed document into smaller pieces with UI feature classification.
        
        Args:
            parsed_document: The parsed document to chunk
            additional_metadata: Optional additional metadata to include
            
        Returns:
            List of DocumentChunk objects
        """
        try:
            if not parsed_document or not parsed_document.content:
                logger.warning("Empty or invalid document provided for chunking")
                return []
            
            logger.info(f"Chunking document: {parsed_document.metadata.get('file_name', 'unknown')}")
            
            # Prepare base metadata
            base_metadata = parsed_document.metadata.copy()
            if additional_metadata:
                base_metadata.update(additional_metadata)
            
            # Extract heading-based sections
            sections = await self._extract_heading_sections(parsed_document.content)

            # Process each section into chunks
            document_chunks = []
            for i, section in enumerate(sections):
                chunk = await self._create_heading_chunk(
                    section, base_metadata, i, parsed_document.content
                )
                if chunk:
                    document_chunks.append(chunk)
            
            # Process tables as separate chunks if present
            if parsed_document.tables:
                table_chunks = await self._process_tables(parsed_document.tables, base_metadata, len(document_chunks))
                document_chunks.extend(table_chunks)
            
            logger.info(f"Created {len(document_chunks)} chunks from document")
            return document_chunks
            
        except Exception as e:
            logger.error(f"Error chunking document: {str(e)}")
            return []
    
    async def _extract_heading_sections(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract sections based on markdown headings.

        Args:
            content: Document content to process

        Returns:
            List of section dictionaries with heading info and content
        """
        try:
            lines = content.split('\n')
            sections = []
            current_section = None

            for line_num, line in enumerate(lines):
                line = line.strip()

                # Check if line is a heading
                if line.startswith('#'):
                    # Save previous section if exists
                    if current_section:
                        sections.append(current_section)

                    # Start new section
                    heading_level = len(line) - len(line.lstrip('#'))
                    heading_text = line.lstrip('#').strip()

                    current_section = {
                        'heading_level': heading_level,
                        'heading_text': heading_text,
                        'content_lines': [line],
                        'start_line': line_num,
                        'end_line': line_num
                    }
                elif current_section:
                    # Add line to current section
                    current_section['content_lines'].append(line)
                    current_section['end_line'] = line_num
                else:
                    # Content before first heading - create a general section
                    if not sections:
                        current_section = {
                            'heading_level': 0,
                            'heading_text': 'Document Introduction',
                            'content_lines': [line],
                            'start_line': line_num,
                            'end_line': line_num
                        }
                    elif line.strip():  # Only add non-empty lines
                        current_section['content_lines'].append(line)
                        current_section['end_line'] = line_num

            # Add final section
            if current_section:
                sections.append(current_section)

            # Convert content_lines to content string
            for section in sections:
                section['content'] = '\n'.join(section['content_lines'])
                del section['content_lines']  # Clean up

            logger.info(f"Extracted {len(sections)} heading-based sections")
            return sections

        except Exception as e:
            logger.error(f"Error extracting heading sections: {str(e)}")
            # Fallback: treat entire content as one section
            return [{
                'heading_level': 1,
                'heading_text': 'Document Content',
                'content': content,
                'start_line': 0,
                'end_line': len(content.split('\n')) - 1
            }]

    async def _create_heading_chunk(self, section: Dict[str, Any], base_metadata: Dict[str, Any],
                                  chunk_index: int, full_content: str) -> Optional[DocumentChunk]:
        """
        Create a DocumentChunk from a heading section with UI feature classification.

        Args:
            section: Section dictionary with heading and content info
            base_metadata: Base metadata for the chunk
            chunk_index: Index of this chunk
            full_content: Full document content for position calculation

        Returns:
            DocumentChunk or None if invalid
        """
        try:
            content = section['content'].strip()

            # Skip empty sections
            if not content or len(content) < self.config.min_chunk_length:
                logger.debug(f"Skipping empty or too short section: {section['heading_text']}")
                return None

            # Calculate position in original document
            start_char = full_content.find(content)
            if start_char == -1:
                start_char = 0  # Fallback if exact match not found
            end_char = start_char + len(content)

            # Classify UI features
            ui_features = self._classify_ui_features(content)

            # Create chunk metadata
            chunk_metadata = base_metadata.copy()
            chunk_metadata.update({
                "chunk_index": chunk_index,
                "chunk_length": len(content),
                "ui_features": [feature.value for feature in ui_features],
                "has_ui_classification": len(ui_features) > 0,
                "heading_level": section['heading_level'],
                "heading_text": section['heading_text'],
                "section_start_line": section['start_line'],
                "section_end_line": section['end_line']
            })

            return DocumentChunk(
                content=content,
                metadata=chunk_metadata,
                ui_features=ui_features,
                chunk_index=chunk_index,
                start_char=start_char,
                end_char=end_char,
                word_count=len(content.split()),
                heading_level=section['heading_level'],
                heading_text=section['heading_text']
            )

        except Exception as e:
            logger.error(f"Error creating heading chunk: {str(e)}")
            return None

    def _classify_ui_features(self, text: str) -> Set[UIFeatureType]:
        """
        Classify chunk content by UI features.

        Args:
            text: Text content to classify

        Returns:
            Set of detected UI feature types
        """
        detected_features = set()
        text_lower = text.lower()

        for feature_type, pattern in self.feature_patterns.items():
            if pattern.search(text_lower):
                detected_features.add(feature_type)

        # If no specific features detected, mark as general
        if not detected_features:
            detected_features.add(UIFeatureType.GENERAL)

        return detected_features
    
    def _simple_chunk_split(self, content: str) -> List[str]:
        """
        Fallback simple chunking method.
        
        Args:
            content: Content to chunk
            
        Returns:
            List of chunks
        """
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + self.config.chunk_size
            
            # Try to break at sentence boundary
            if end < len(content):
                # Look for sentence endings within overlap range
                search_start = max(start, end - self.config.chunk_overlap)
                sentence_end = content.rfind('.', search_start, end)
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - self.config.chunk_overlap
        
        return chunks
    
    async def _split_oversized_chunk(self, chunk_text: str) -> List[str]:
        """
        Split chunks that exceed maximum length.
        
        Args:
            chunk_text: Text to split
            
        Returns:
            List of smaller chunks
        """
        sub_chunks = []
        start = 0
        
        while start < len(chunk_text):
            end = start + self.config.max_chunk_length
            
            if end >= len(chunk_text):
                sub_chunks.append(chunk_text[start:].strip())
                break
            
            # Find good break point (sentence or paragraph)
            break_point = chunk_text.rfind('.', start, end)
            if break_point == -1:
                break_point = chunk_text.rfind('\n', start, end)
            if break_point == -1:
                break_point = chunk_text.rfind(' ', start, end)
            
            if break_point > start:
                end = break_point + 1
            
            sub_chunk = chunk_text[start:end].strip()
            if sub_chunk:
                sub_chunks.append(sub_chunk)
            
            start = end
        
        return sub_chunks
    
    async def _create_chunk(self, chunk_text: str, base_metadata: Dict[str, Any], chunk_index: int, full_content: str) -> Optional[DocumentChunk]:
        """
        Create a DocumentChunk with activity classification.
        
        Args:
            chunk_text: The chunk content
            base_metadata: Base metadata from document
            chunk_index: Index of this chunk
            full_content: Full document content for position calculation
            
        Returns:
            DocumentChunk or None if invalid
        """
        try:
            # Calculate position in original document
            start_char = full_content.find(chunk_text)
            if start_char == -1:
                start_char = 0  # Fallback if exact match not found
            end_char = start_char + len(chunk_text)
            
            # Classify UI features
            ui_features = self._classify_ui_features(chunk_text)

            # Create chunk metadata
            chunk_metadata = base_metadata.copy()
            chunk_metadata.update({
                "chunk_index": chunk_index,
                "chunk_length": len(chunk_text),
                "ui_features": [feature.value for feature in ui_features],
                "has_ui_feature_classification": len(ui_features) > 0
            })

            return DocumentChunk(
                content=chunk_text,
                metadata=chunk_metadata,
                ui_features=ui_features,
                chunk_index=chunk_index,
                start_char=start_char,
                end_char=end_char,
                word_count=len(chunk_text.split())
            )
            
        except Exception as e:
            logger.error(f"Error creating chunk: {str(e)}")
            return None
    

    
    async def _process_tables(self, tables: List[Dict[str, Any]], base_metadata: Dict[str, Any]) -> List[DocumentChunk]:
        """
        Process extracted tables as separate chunks.

        Args:
            tables: List of table data
            base_metadata: Base metadata

        Returns:
            List of table chunks
        """
        table_chunks = []

        for i, table in enumerate(tables):
            table_content = table.get('content', '')
            if not table_content or len(table_content) < self.config.min_chunk_length:
                continue

            # Create table-specific metadata
            table_metadata = base_metadata.copy()
            table_metadata.update({
                "content_type": "table",
                "table_index": i,
                "bbox": table.get('bbox'),
                "page": table.get('page')
            })

            # Classify table UI features
            ui_features = self._classify_ui_features(table_content)

            chunk = DocumentChunk(
                content=f"Table {i + 1}:\n{table_content}",
                metadata=table_metadata,
                ui_features=ui_features,
                chunk_index=1000 + i,  # Use high index for tables
                start_char=0,  # Tables don't have position in main text
                end_char=len(table_content),
                word_count=len(table_content.split()),
                heading_level=0,
                heading_text=f"Table {i + 1}"
            )

            table_chunks.append(chunk)

        return table_chunks
    
    async def chunk_multiple(self, parsed_documents: List[ParsedDocument], metadata_list: Optional[List[Dict[str, Any]]] = None) -> Dict[str, List[DocumentChunk]]:
        """
        Chunk multiple documents concurrently.
        
        Args:
            parsed_documents: List of parsed documents
            metadata_list: Optional list of additional metadata
            
        Returns:
            Dict mapping document names to chunk lists
        """
        tasks = []
        
        for i, doc in enumerate(parsed_documents):
            additional_metadata = metadata_list[i] if metadata_list and i < len(metadata_list) else None
            task = self.chunk(doc, additional_metadata)
            doc_name = doc.metadata.get('file_name', f'document_{i}')
            tasks.append((doc_name, task))
        
        results = {}
        for doc_name, task in tasks:
            try:
                chunks = await task
                results[doc_name] = chunks
            except Exception as e:
                logger.error(f"Error chunking document {doc_name}: {str(e)}")
                results[doc_name] = []
        
        return results
    
    def get_ui_feature_stats(self, chunks: List[DocumentChunk]) -> Dict[str, int]:
        """
        Get statistics about UI feature classification in chunks.

        Args:
            chunks: List of document chunks

        Returns:
            Dict with UI feature type counts
        """
        stats = {feature.value: 0 for feature in UIFeatureType}

        for chunk in chunks:
            for feature in chunk.ui_features:
                stats[feature.value] += 1

        return stats
