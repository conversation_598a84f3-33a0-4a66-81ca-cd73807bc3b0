"""
Document Parser using Docling for comprehensive document processing.

This module handles parsing of various document formats including PDF, DOCX, PPTX,
HTML, and text files using the Docling library for high-quality content extraction.
"""

from typing import Dict, Any, Optional, List
import asyncio
from pathlib import Path
from core.logger import logger

from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.document_converter import PdfFormatOption

from ..config import ParsingConfig


class ParsedDocument:
    """
    Container for parsed document content and metadata.
    """
    
    def __init__(
        self,
        content: str,
        metadata: Dict[str, Any],
        tables: Optional[List[Dict[str, Any]]] = None,
        images: Optional[List[Dict[str, Any]]] = None
    ):
        """
        Initialize parsed document.
        
        Args:
            content: Extracted text content
            metadata: Document metadata
            tables: Extracted tables (if any)
            images: Extracted images (if any)
        """
        self.content = content
        self.metadata = metadata
        self.tables = tables or []
        self.images = images or []
        self.word_count = len(content.split()) if content else 0


class DocumentParser:
    """
    Document parser using Docling for comprehensive document processing.
    
    Supports parsing of PDF, DOCX, PPTX, HTML, Markdown, and text files
    with advanced features like OCR, table extraction, and image processing.
    """
    
    def __init__(self, config: ParsingConfig):
        """
        Initialize the document parser.
        
        Args:
            config: Parsing configuration
        """
        self.config = config
        self.converter = self._setup_converter()
        logger.info("Document parser initialized")
    
    def _setup_converter(self) -> DocumentConverter:
        """
        Set up the Docling document converter with configuration.
        
        Returns:
            DocumentConverter: Configured converter instance
        """
        # Configure PDF pipeline options
        pdf_options = PdfPipelineOptions()
        pdf_options.do_ocr = self.config.enable_ocr
        pdf_options.do_table_structure = self.config.extract_tables
        
        # Set up format options
        format_options = {
            InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_options)
        }
        
        # Create converter
        converter = DocumentConverter(
            format_options=format_options
        )
        
        return converter
    
    async def parse(self, document_path: str) -> Optional[ParsedDocument]:
        """
        Parse a document and extract content, tables, and images.
        
        Args:
            document_path: Path to the document to parse
            
        Returns:
            ParsedDocument: Parsed document content or None if parsing failed
        """
        try:
            document_path = Path(document_path)
            
            # Validate file exists and size
            if not document_path.exists():
                logger.error(f"Document not found: {document_path}")
                return None
            
            file_size_mb = document_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.config.max_file_size_mb:
                logger.error(f"Document too large: {file_size_mb:.2f}MB > {self.config.max_file_size_mb}MB")
                return None
            
            # Check file format
            file_extension = document_path.suffix.lower().lstrip('.')
            if file_extension not in self.config.supported_formats:
                logger.error(f"Unsupported format: {file_extension}")
                return None
            
            logger.info(f"Parsing document: {document_path}")
            
            # Parse document using Docling
            result = await asyncio.to_thread(
                self._parse_with_timeout,
                str(document_path)
            )
            
            if not result:
                logger.error(f"Failed to parse document: {document_path}")
                return None
            
            # Extract content and metadata
            parsed_doc = self._extract_content(result, document_path)
            
            logger.info(f"Successfully parsed document: {document_path} ({parsed_doc.word_count} words)")
            return parsed_doc
            
        except Exception as e:
            logger.error(f"Error parsing document {document_path}: {str(e)}")
            return None
    
    def _parse_with_timeout(self, document_path: str):
        """
        Parse document with timeout handling.
        
        Args:
            document_path: Path to document
            
        Returns:
            Parsed document result or None
        """
        try:
            # Convert document
            result = self.converter.convert(document_path)
            return result
            
        except Exception as e:
            logger.error(f"Docling parsing error: {str(e)}")
            return None
    
    def _extract_content(self, result, document_path: Path) -> ParsedDocument:
        """
        Extract content, tables, and images from Docling result.
        
        Args:
            result: Docling conversion result
            document_path: Original document path
            
        Returns:
            ParsedDocument: Extracted content and metadata
        """
        # Extract main text content
        content = result.document.export_to_markdown()
        
        # Extract metadata
        metadata = {
            "file_name": document_path.name,
            "file_path": str(document_path),
            "file_size": document_path.stat().st_size,
            "file_extension": document_path.suffix.lower(),
            "parsing_timestamp": asyncio.get_event_loop().time(),
            "docling_version": "2.43.0"  # Current version
        }
        
        # Extract tables if enabled
        tables = []
        if self.config.extract_tables and hasattr(result.document, 'tables'):
            for table in result.document.tables:
                table_data = {
                    "content": table.export_to_markdown() if hasattr(table, 'export_to_markdown') else str(table),
                    "bbox": getattr(table, 'bbox', None),
                    "page": getattr(table, 'page', None)
                }
                tables.append(table_data)
        
        # Extract images if enabled
        images = []
        if self.config.extract_images and hasattr(result.document, 'pictures'):
            for image in result.document.pictures:
                image_data = {
                    "caption": getattr(image, 'caption', ''),
                    "bbox": getattr(image, 'bbox', None),
                    "page": getattr(image, 'page', None)
                }
                images.append(image_data)
        
        return ParsedDocument(
            content=content,
            metadata=metadata,
            tables=tables,
            images=images
        )
    
    async def parse_multiple(self, document_paths: List[str]) -> Dict[str, Optional[ParsedDocument]]:
        """
        Parse multiple documents concurrently.
        
        Args:
            document_paths: List of document paths to parse
            
        Returns:
            Dict mapping document paths to parsed results
        """
        tasks = []
        for doc_path in document_paths:
            task = self.parse(doc_path)
            tasks.append((doc_path, task))
        
        results = {}
        for doc_path, task in tasks:
            try:
                result = await task
                results[doc_path] = result
            except Exception as e:
                logger.error(f"Error parsing {doc_path}: {str(e)}")
                results[doc_path] = None
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported document formats.
        
        Returns:
            List of supported file extensions
        """
        return self.config.supported_formats.copy()
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the parser.
        
        Returns:
            Dict containing health status
        """
        try:
            # Test basic functionality
            test_result = {
                "healthy": True,
                "supported_formats": self.config.supported_formats,
                "max_file_size_mb": self.config.max_file_size_mb,
                "ocr_enabled": self.config.enable_ocr,
                "table_extraction_enabled": self.config.extract_tables,
                "image_extraction_enabled": self.config.extract_images
            }
            
            return test_result
            
        except Exception as e:
            logger.error(f"Parser health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e)
            }
