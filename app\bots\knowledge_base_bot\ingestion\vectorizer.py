"""
Vectorizer for converting text chunks to embeddings using OpenAI models.

This module handles the vectorization of document chunks using OpenAI's
embedding models with proper rate limiting, batching, and error handling.
"""

from typing import List, Dict, Any, Optional
import asyncio
import time
from dataclasses import dataclass
from core.logger import logger
import numpy as np

from openai import AsyncOpenAI

from ..config import VectorizationConfig
from .document_chunker import DocumentChunk


@dataclass
class VectorizedChunk:
    """
    Container for a vectorized document chunk.
    """
    chunk: DocumentChunk
    vector: List[float]
    embedding_model: str
    embedding_timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for storage."""
        return {
            "content": self.chunk.content,
            "metadata": self.chunk.metadata,
            "ui_features": [feature.value for feature in self.chunk.ui_features],
            "vector": self.vector,
            "embedding_model": self.embedding_model,
            "embedding_timestamp": self.embedding_timestamp,
            "chunk_index": self.chunk.chunk_index,
            "word_count": self.chunk.word_count,
            "heading_level": self.chunk.heading_level,
            "heading_text": self.chunk.heading_text
        }


class RateLimiter:
    """Simple rate limiter for API calls."""
    
    def __init__(self, requests_per_minute: int):
        self.requests_per_minute = requests_per_minute
        self.min_interval = 60.0 / requests_per_minute
        self.last_request_time = 0.0
    
    async def wait_if_needed(self):
        """Wait if necessary to respect rate limits."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            wait_time = self.min_interval - time_since_last
            await asyncio.sleep(wait_time)
        
        self.last_request_time = time.time()


class Vectorizer:
    """
    Vectorizer for converting text chunks to embeddings using OpenAI models.
    
    Handles batching, rate limiting, retries, and error recovery for
    robust embedding generation at scale.
    """
    
    def __init__(self, config: VectorizationConfig):
        """
        Initialize the vectorizer.
        
        Args:
            config: Vectorization configuration
        """
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.rate_limiter = RateLimiter(config.requests_per_minute)
        logger.info(f"Vectorizer initialized with model: {config.model_name}")
    
    async def vectorize_chunks(self, chunks: List[DocumentChunk]) -> List[VectorizedChunk]:
        """
        Vectorize a list of document chunks.
        
        Args:
            chunks: List of document chunks to vectorize
            
        Returns:
            List of vectorized chunks
        """
        if not chunks:
            logger.warning("No chunks provided for vectorization")
            return []
        
        logger.info(f"Vectorizing {len(chunks)} chunks")
        
        try:
            # Process chunks in batches
            vectorized_chunks = []
            
            for i in range(0, len(chunks), self.config.batch_size):
                batch = chunks[i:i + self.config.batch_size]
                batch_results = await self._vectorize_batch(batch)
                vectorized_chunks.extend(batch_results)
                
                # Log progress
                progress = min(i + self.config.batch_size, len(chunks))
                logger.info(f"Vectorized {progress}/{len(chunks)} chunks")
            
            logger.info(f"Successfully vectorized {len(vectorized_chunks)} chunks")
            return vectorized_chunks
            
        except Exception as e:
            logger.error(f"Error vectorizing chunks: {str(e)}")
            return []
    
    async def _vectorize_batch(self, chunks: List[DocumentChunk]) -> List[VectorizedChunk]:
        """
        Vectorize a batch of chunks.
        
        Args:
            chunks: Batch of chunks to vectorize
            
        Returns:
            List of vectorized chunks
        """
        texts = [chunk.content for chunk in chunks]
        vectors = await self._get_embeddings(texts)
        
        if not vectors or len(vectors) != len(chunks):
            logger.error(f"Embedding count mismatch: got {len(vectors) if vectors else 0}, expected {len(chunks)}")
            return []
        
        # Create vectorized chunks
        vectorized_chunks = []
        current_time = time.time()
        
        for chunk, vector in zip(chunks, vectors):
            vectorized_chunk = VectorizedChunk(
                chunk=chunk,
                vector=vector,
                embedding_model=self.config.model_name,
                embedding_timestamp=current_time
            )
            vectorized_chunks.append(vectorized_chunk)
        
        return vectorized_chunks
    
    async def _get_embeddings(self, texts: List[str]) -> Optional[List[List[float]]]:
        """
        Get embeddings for a list of texts with retry logic.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors or None if failed
        """
        for attempt in range(self.config.max_retries):
            try:
                # Apply rate limiting
                await self.rate_limiter.wait_if_needed()
                
                # Call OpenAI API
                response = await self.client.embeddings.create(
                    model=self.config.model_name,
                    input=texts,
                    encoding_format="float"
                )
                
                # Extract embeddings
                embeddings = [data.embedding for data in response.data]
                
                # Validate embedding dimensions
                for embedding in embeddings:
                    if len(embedding) != self.config.embedding_dimension:
                        logger.error(f"Unexpected embedding dimension: {len(embedding)} != {self.config.embedding_dimension}")
                        return None
                
                return embeddings
                
            except Exception as e:
                if "rate_limit" in str(e).lower():
                    logger.warning(f"Rate limit hit, attempt {attempt + 1}/{self.config.max_retries}")
                    if attempt < self.config.max_retries - 1:
                        wait_time = self.config.retry_delay * (2 ** attempt)  # Exponential backoff
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"Rate limit exceeded after {self.config.max_retries} attempts")
                        return None
                else:
                    logger.error(f"OpenAI API error: {str(e)}")
                    if attempt < self.config.max_retries - 1:
                        await asyncio.sleep(self.config.retry_delay)
                    else:
                        return None
                    
            except Exception as e:
                logger.error(f"Unexpected error getting embeddings: {str(e)}")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.retry_delay)
                else:
                    return None
        
        return None
    
    async def vectorize_single_text(self, text: str) -> Optional[List[float]]:
        """
        Vectorize a single text string.
        
        Args:
            text: Text to vectorize
            
        Returns:
            Embedding vector or None if failed
        """
        try:
            embeddings = await self._get_embeddings([text])
            return embeddings[0] if embeddings else None
            
        except Exception as e:
            logger.error(f"Error vectorizing single text: {str(e)}")
            return None
    
    async def vectorize_query(self, query: str) -> Optional[List[float]]:
        """
        Vectorize a query string for retrieval.
        
        Args:
            query: Query text to vectorize
            
        Returns:
            Query embedding vector or None if failed
        """
        return await self.vectorize_single_text(query)
    
    def calculate_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors.
        
        Args:
            vector1: First vector
            vector2: Second vector
            
        Returns:
            Cosine similarity score
        """
        try:
            # Convert to numpy arrays
            v1 = np.array(vector1)
            v2 = np.array(vector2)
            
            # Calculate cosine similarity
            dot_product = np.dot(v1, v2)
            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the vectorizer.
        
        Returns:
            Dict containing health status
        """
        try:
            # Test with a simple text
            test_text = "This is a test for the vectorizer health check."
            test_vector = await self.vectorize_single_text(test_text)
            
            if test_vector and len(test_vector) == self.config.embedding_dimension:
                return {
                    "healthy": True,
                    "model": self.config.model_name,
                    "embedding_dimension": self.config.embedding_dimension,
                    "batch_size": self.config.batch_size,
                    "test_embedding_length": len(test_vector)
                }
            else:
                return {
                    "healthy": False,
                    "error": "Test embedding failed or wrong dimension"
                }
                
        except Exception as e:
            logger.error(f"Vectorizer health check failed: {str(e)}")
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get vectorizer statistics and configuration.
        
        Returns:
            Dict with vectorizer stats
        """
        return {
            "model_name": self.config.model_name,
            "embedding_dimension": self.config.embedding_dimension,
            "batch_size": self.config.batch_size,
            "max_retries": self.config.max_retries,
            "requests_per_minute": self.config.requests_per_minute,
            "retry_delay": self.config.retry_delay
        }
