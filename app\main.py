from pathlib import Path
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from core.config import settings
from core.exception_handler import add_global_exception_handler
from api.router import api_router
from api.swagger import swagger_router
from api.middleware.auth_middleware import AuthMiddleware

# Load environment variables from .env file
env_path = Path(__file__).parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description=settings.DESCRIPTION,
    docs_url=None,  # Disable default Swagger
    redoc_url=None,
    openapi_url=None,
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update for production use
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Auth Middleware
app.add_middleware(AuthMiddleware)

# Add the global exception handler
add_global_exception_handler(app)

# Register swagger UI
app.include_router(swagger_router, prefix="/swagger", tags=["swagger"])

# Register all application routes
app.include_router(api_router, prefix=settings.API_V1_STR)

# Run only when executed directly (not needed in production WSGI servers)
if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
